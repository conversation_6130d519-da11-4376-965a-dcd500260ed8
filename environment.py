"""3D Environment with 360-degree panoramic background"""

import pygame
import numpy as np
import math
from typing import Tu<PERSON>, Optional, List
from PIL import Image
from loguru import logger as log
import os

from camera import Camera
from entities import Pigeon, Projectile, EntityManager


class PanoramaRenderer:
    """Renders 360-degree panoramic background"""

    def __init__(self, window_size: Tuple[int, int]):
        self.window_width, self.window_height = window_size
        self.panorama_surface = None
        self.default_background = None
        self._create_default_background()

    def _create_default_background(self):
        """Create a default gradient background if no panorama is available"""
        self.default_background = pygame.Surface(
            (self.window_width, self.window_height)
        )

        # Create sky gradient
        for y in range(self.window_height):
            # Sky color gradient from light blue to darker blue
            ratio = y / self.window_height
            if ratio < 0.5:
                # Sky
                r = int(135 + (185 - 135) * ratio * 2)
                g = int(206 + (235 - 206) * ratio * 2)
                b = int(235 + (255 - 235) * ratio * 2)
            else:
                # Ground
                ground_ratio = (ratio - 0.5) * 2
                r = int(150 - 50 * ground_ratio)
                g = int(150 - 50 * ground_ratio)
                b = int(120 - 40 * ground_ratio)

            pygame.draw.line(
                self.default_background, (r, g, b), (0, y), (self.window_width, y)
            )

        # Add some building silhouettes
        building_color = (80, 80, 80)
        for i in range(5):
            x = i * self.window_width // 5
            width = self.window_width // 8
            height = np.random.randint(self.window_height // 4, self.window_height // 2)
            y = self.window_height - height
            pygame.draw.rect(
                self.default_background, building_color, (x, y, width, height)
            )

    def load_panorama(self, image_path: str) -> bool:
        """Load a 360-degree panorama image"""
        try:
            if os.path.exists(image_path):
                # Load with PIL first for better format support
                pil_image = Image.open(image_path)
                # Convert to RGB if necessary
                if pil_image.mode != "RGB":
                    pil_image = pil_image.convert("RGB")

                # Convert to pygame surface
                image_string = pil_image.tobytes()
                self.panorama_surface = pygame.image.fromstring(
                    image_string, pil_image.size, "RGB"
                )
                log.info(f"Loaded panorama from {image_path}")
                return True
            else:
                log.warning(f"Panorama file not found: {image_path}")
                return False
        except Exception as e:
            log.error(f"Failed to load panorama: {e}")
            return False

    def render(self, screen: pygame.Surface, camera: Camera):
        """Render the panoramic view based on camera orientation"""
        if self.panorama_surface:
            self._render_panorama(screen, camera)
        else:
            self._render_default(screen, camera)

    def _render_panorama(self, screen: pygame.Surface, camera: Camera):
        """Render actual panorama image"""
        pano_width = self.panorama_surface.get_width()
        pano_height = self.panorama_surface.get_height()

        # Calculate the portion of panorama to display
        # Horizontal: based on yaw and FOV
        fov_ratio = camera.state.fov / 360.0
        view_width = int(pano_width * fov_ratio)

        # Calculate left edge of view in panorama
        yaw_ratio = camera.state.yaw / 360.0
        center_x = int(pano_width * yaw_ratio)
        left_x = center_x - view_width // 2

        # Vertical: based on pitch
        pitch_ratio = (camera.state.pitch + 90) / 180.0  # Convert -90..90 to 0..1
        center_y = int(pano_height * (1 - pitch_ratio))  # Invert for screen coordinates

        # Calculate vertical view range based on FOV and aspect ratio
        aspect_ratio = self.window_width / self.window_height
        vertical_fov = camera.state.fov / aspect_ratio
        vertical_fov_ratio = vertical_fov / 180.0
        view_height = int(pano_height * vertical_fov_ratio)

        top_y = center_y - view_height // 2

        # Create view surface
        view_surface = pygame.Surface((view_width, view_height))

        # Handle wrapping for 360 panorama
        if left_x < 0:
            # Wrap around left edge
            right_part_width = -left_x
            left_part_width = view_width - right_part_width

            # Right part of panorama
            view_surface.blit(
                self.panorama_surface,
                (0, 0),
                (pano_width + left_x, top_y, right_part_width, view_height),
            )
            # Left part of panorama
            view_surface.blit(
                self.panorama_surface,
                (right_part_width, 0),
                (0, top_y, left_part_width, view_height),
            )
        elif left_x + view_width > pano_width:
            # Wrap around right edge
            left_part_width = pano_width - left_x
            right_part_width = view_width - left_part_width

            # Left part of panorama
            view_surface.blit(
                self.panorama_surface,
                (0, 0),
                (left_x, top_y, left_part_width, view_height),
            )
            # Right part of panorama
            view_surface.blit(
                self.panorama_surface,
                (left_part_width, 0),
                (0, top_y, right_part_width, view_height),
            )
        else:
            # No wrapping needed
            view_surface.blit(
                self.panorama_surface, (0, 0), (left_x, top_y, view_width, view_height)
            )

        # Scale to window size
        scaled_surface = pygame.transform.scale(
            view_surface, (self.window_width, self.window_height)
        )
        screen.blit(scaled_surface, (0, 0))

    def _render_default(self, screen: pygame.Surface, camera: Camera):
        """Render default background with rotation effect"""
        # For default background, create a rotating effect
        offset_x = int(
            camera.state.yaw * self.window_width / 90
        )  # Repeat every 90 degrees
        offset_y = int(camera.state.pitch * 5)  # Slight vertical shift

        # Blit with wrapping
        screen.blit(self.default_background, (-offset_x % self.window_width, offset_y))
        screen.blit(
            self.default_background,
            ((-offset_x % self.window_width) - self.window_width, offset_y),
        )


class Environment:
    """Main environment manager"""

    def __init__(self, window_size: Tuple[int, int]):
        self.window_size = window_size
        self.panorama_renderer = PanoramaRenderer(window_size)

        # Visual settings
        self.crosshair_size = 20
        self.pigeon_color = (128, 128, 128)
        self.projectile_color = (255, 255, 0)
        self.crosshair_color = (255, 0, 0)

    def load_panorama(self, path: str):
        """Load panorama image"""
        return self.panorama_renderer.load_panorama(path)

    def render(
        self,
        screen: pygame.Surface,
        camera: Camera,
        entity_manager: EntityManager,
        show_debug: bool = False,
    ):
        """Render complete environment"""
        # Render background
        self.panorama_renderer.render(screen, camera)

        # Render entities
        self._render_pigeons(screen, camera, entity_manager.pigeons)
        self._render_projectiles(screen, camera, entity_manager.projectiles)

        # Render UI elements
        self._render_crosshair(screen)

        if show_debug:
            self._render_debug_info(screen, camera, entity_manager)

    def _render_pigeons(
        self, screen: pygame.Surface, camera: Camera, pigeons: List[Pigeon]
    ):
        """Render pigeons as gray squares with distance-based sizing"""
        for pigeon in pigeons:
            if not pigeon.active:
                continue

            yaw, pitch, distance = pigeon.to_spherical()
            screen_pos = camera.angles_to_screen(yaw, pitch)

            if screen_pos:
                # Use distance-based apparent size
                apparent_size = int(pigeon.get_apparent_size())

                # Different colors for different states
                if pigeon.is_fleeing:
                    color = (255, 100, 100)  # Red for fleeing
                    border_color = (200, 50, 50)
                elif pigeon.shots_taken_at > 0:
                    color = (200, 200, 100)  # Yellow for targeted
                    border_color = (150, 150, 50)
                else:
                    color = self.pigeon_color  # Gray for normal
                    border_color = (64, 64, 64)

                # Draw pigeon as square
                rect = pygame.Rect(
                    screen_pos[0] - apparent_size // 2,
                    screen_pos[1] - apparent_size // 2,
                    apparent_size,
                    apparent_size,
                )
                pygame.draw.rect(screen, color, rect)
                pygame.draw.rect(screen, border_color, rect, 2)  # Border

                # Show shot count for targeted pigeons
                if pigeon.shots_taken_at > 0:
                    font = pygame.font.Font(None, 16)
                    text = font.render(
                        str(pigeon.shots_taken_at), True, (255, 255, 255)
                    )
                    text_pos = (
                        screen_pos[0] - 5,
                        screen_pos[1] - apparent_size // 2 - 15,
                    )
                    screen.blit(text, text_pos)

    def _render_projectiles(
        self, screen: pygame.Surface, camera: Camera, projectiles: List[Projectile]
    ):
        """Render projectiles"""
        for projectile in projectiles:
            if not projectile.active:
                continue

            # Convert 3D position to spherical
            pos = projectile.position
            distance = np.linalg.norm(pos)
            if distance < 0.001:
                continue

            yaw = math.degrees(math.atan2(pos[0], pos[2])) % 360
            pitch = math.degrees(math.asin(np.clip(pos[1] / distance, -1, 1)))

            screen_pos = camera.angles_to_screen(yaw, pitch)

            if screen_pos:
                # Draw projectile as small circle
                pygame.draw.circle(screen, self.projectile_color, screen_pos, 3)
                pygame.draw.circle(
                    screen, (255, 255, 255), screen_pos, 4, 1
                )  # White outline

    def _render_crosshair(self, screen: pygame.Surface):
        """Render targeting crosshair"""
        center_x = self.window_size[0] // 2
        center_y = self.window_size[1] // 2

        # Draw crosshair lines
        pygame.draw.line(
            screen,
            self.crosshair_color,
            (center_x - self.crosshair_size, center_y),
            (center_x + self.crosshair_size, center_y),
            2,
        )
        pygame.draw.line(
            screen,
            self.crosshair_color,
            (center_x, center_y - self.crosshair_size),
            (center_x, center_y + self.crosshair_size),
            2,
        )

        # Draw center dot
        pygame.draw.circle(screen, self.crosshair_color, (center_x, center_y), 3)

    def _render_debug_info(
        self, screen: pygame.Surface, camera: Camera, entity_manager: EntityManager
    ):
        """Render debug information"""
        font = pygame.font.Font(None, 24)
        debug_info = [
            f"Camera: Yaw={camera.state.yaw:.1f}° Pitch={camera.state.pitch:.1f}°",
            f"Pigeons: {len([p for p in entity_manager.pigeons if p.active])}",
            f"Projectiles: {len([p for p in entity_manager.projectiles if p.active])}",
        ]

        y_offset = 10
        for line in debug_info:
            text = font.render(line, True, (255, 255, 255))
            text_rect = text.get_rect()

            # Add background for readability
            bg_rect = pygame.Rect(
                10 - 5, y_offset - 2, text_rect.width + 10, text_rect.height + 4
            )
            pygame.draw.rect(screen, (0, 0, 0), bg_rect)
            pygame.draw.rect(screen, (255, 255, 255), bg_rect, 1)

            screen.blit(text, (10, y_offset))
            y_offset += 30
