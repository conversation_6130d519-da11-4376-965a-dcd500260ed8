<!-- Generated by SpecStory -->

# Multi-Target Pigeon Turret Development (2025-05-27 22:30:06)

_**User**_

@Automated Pigeon Turret Simulation Development 
@Pigeon Turret Experimentation Strategy 

--

MULTI TARGET UPGRADE!!!

---

OK - how about we make a new file for making a multi-target path?
new config: multi_target_max = 10
The multi-target picks n targets and plans 10 shots, then actions them. This is trick so document the issues and solutions well (eg estimating time-to-move-turret as those will add up per movement. Path optimization, could use eg pathfinding algo, will it run fast enough though? algo needs to run FAST as the objects it's tracking move fast.

Think it through first then output a mini-PRD, save to dir called specs.
Then make the feature!

---

_**Assistant**_

I'll analyze the multi-target upgrade requirements and create a comprehensive PRD before implementing the feature.

---



---



---



---



---



---

Now I'll create the specs directory and write the PRD:

---

```bash
mkdir -p specs
```

```
(pigeon-turret-simulator) ➜  songs_with_words_with_friends git:(pigeon-turret) ✗
```

---

```diff
- 
+ # Multi-Target Turret System PRD
+ 
+ ## Overview
+ Enhancement to the pigeon turret simulator to enable targeting and engaging multiple pigeons in a single planning cycle, optimizing the path to maximize hits while minimizing turret movement time.
+ 
+ ## Problem Statement
+ Current system targets one pigeon at a time, missing opportunities to efficiently engage multiple targets when they're clustered or in favorable positions. This leads to:
+ - Inefficient turret movement patterns
+ - Missed opportunities when multiple targets are nearby
+ - Lower overall hit rate when facing swarms
+ 
+ ## Goals
+ 1. **Primary**: Increase overall hit rate by planning multi-target sequences
+ 2. **Secondary**: Minimize turret movement time between shots
+ 3. **Tertiary**: Maintain real-time performance (<10ms planning time)
+ 
+ ## Technical Challenges & Solutions
+ 
+ ### 1. Dynamic Path Planning
+ **Challenge**: Targets are moving while turret rotates between shots
+ **Solution**: 
+ - Predict target positions at shot time using velocity vectors
+ - Account for turret rotation time in predictions
+ - Use time-parameterized trajectories
+ 
+ ### 2. Target Selection
+ **Challenge**: Choosing optimal subset from many targets
+ **Solution**: Multi-factor scoring system:
+ ```
+ score = w1 * (1/distance) + w2 * threat_level + w3 * cluster_density + w4 * hit_probability
+ ```
+ - Distance: Closer targets score higher
+ - Threat level: Targets moving toward center score higher
+ - Cluster density: Targets near other targets score higher
+ - Hit probability: Based on target speed and predictability
+ 
+ ### 3. Path Optimization
+ **Challenge**: TSP-like problem but with moving targets and time constraints
+ **Solutions** (in order of implementation):
+ 1. **Greedy Nearest Neighbor**: O(n²) - baseline algorithm
+ 2. **Angle-Based Sorting**: O(n log n) - minimize rotation by sorting by angle
+ 3. **Dynamic Programming**: O(n² * 2^n) - optimal for small n
+ 4. **2-opt Improvement**: O(n²) - iterative improvement on greedy solution
+ 
+ ### 4. Real-time Performance
+ **Challenge**: Must complete planning in <10ms
+ **Solution**:
+ - Hierarchical approach: start simple, refine if time allows
+ - Vectorized numpy operations
+ - Pre-computed lookup tables for common calculations
+ - Adaptive algorithm selection based on target count
+ 
+ ## Implementation Design
+ 
+ ### Core Components
+ 
+ ```python
+ class MultiTargetPlanner:
+     def __init__(self, turret_rotation_speed, shot_delay):
+         self.turret_rotation_speed = turret_rotation_speed  # degrees/second
+         self.shot_delay = shot_delay  # seconds between shots
+         
+     def plan_engagement(self, targets, current_turret_angle, max_targets=10):
+         """Main planning method"""
+         # 1. Score and select targets
+         selected_targets = self.select_targets(targets, max_targets)
+         
+         # 2. Optimize firing sequence
+         firing_sequence = self.optimize_path(selected_targets, current_turret_angle)
+         
+         # 3. Generate time-parameterized plan
+         engagement_plan = self.generate_plan(firing_sequence, current_turret_angle)
+         
+         return engagement_plan
+ ```
+ 
+ ### Key Algorithms
+ 
+ #### Target Selection
+ ```python
+ def select_targets(self, targets, max_targets):
+     scores = []
+     for target in targets:
+         score = self.calculate_target_score(target)
+         scores.append((score, target))
+     
+     # Sort by score and take top n
+     scores.sort(reverse=True, key=lambda x: x[0])
+     return [target for _, target in scores[:max_targets]]
+ ```
+ 
+ #### Path Optimization (Greedy)
+ ```python
+ def optimize_path_greedy(self, targets, start_angle):
+     path = []
+     current_angle = start_angle
+     remaining = targets.copy()
+     current_time = 0
+     
+     while remaining:
+         # Find nearest target accounting for movement
+         best_target = None
+         best_time = float('inf')
+         
+         for target in remaining:
+             # Predict where target will be
+             rotation_time = self.calculate_rotation_time(current_angle, target.angle)
+             future_pos = target.predict_position(current_time + rotation_time)
+             total_time = rotation_time + self.shot_delay
+             
+             if total_time < best_time:
+                 best_time = total_time
+                 best_target = target
+         
+         path.append(best_target)
+         remaining.remove(best_target)
+         current_angle = best_target.angle
+         current_time += best_time
+     
+     return path
+ ```
+ 
+ ## Configuration
+ ```python
+ # config.py additions
+ MULTI_TARGET_MAX = 10  # Maximum targets per planning cycle
+ MULTI_TARGET_ENABLED = True
+ TURRET_ROTATION_SPEED = 180  # degrees/second
+ SHOT_DELAY = 0.1  # seconds between shots
+ PATH_PLANNING_TIMEOUT = 0.008  # 8ms max planning time
+ ```
+ 
+ ## Performance Metrics
+ - Planning time per cycle
+ - Average targets engaged per plan
+ - Hit rate improvement vs single-target
+ - Turret efficiency (degrees rotated per hit)
+ 
+ ## Visualization
+ - Show planned path as dotted lines
+ - Number each target in sequence
+ - Display predicted hit positions
+ - Color code by confidence level
+ 
+ ## Fallback Strategy
+ If planning takes too long or fails:
+ 1. Revert to single-target mode
+ 2. Use simple nearest-target selection
+ 3. Log performance issue for analysis
+ 
+ ## Testing Strategy
+ 1. Unit tests for each algorithm component
+ 2. Performance benchmarks for different target counts
+ 3. Simulation tests comparing single vs multi-target
+ 4. Edge cases: no targets, single target, max targets
+ 
+ ## Success Criteria
+ - 20%+ improvement in overall hit rate
+ - Planning completes in <10ms for 95% of frames
+ - No degradation in single-target scenarios
+ - Smooth visual appearance (no jerky movements)
+ 
+ ## Future Enhancements
+ 1. Machine learning for weight optimization
+ 2. Predictive clustering for swarm behavior
+ 3. Cooperative targeting with multiple turrets
+ 4. Priority target designation 
```

---

Now let's implement the multi-target system. First, I'll update the config file:

---

Read file: config.py

---

Now I'll add the multi-target configuration to the config file:

---

Cancelled

---

_**User**_

read it first to not undo changes, then edit

---

_**Assistant**_

I'll read the config file first to ensure I don't undo any changes, then add the multi-target configuration.

---

Read file: config.py

---

I notice the config file has been restructured slightly. Now I'll add the multi-target configuration section:

---

```diff
      BURST_FIRE_MAX_SPEED: float = 80.0  # Only burst fire on slow targets
  
+ 
+ @dataclass
+ class MultiTargetConfig:
+     """Multi-target engagement system configuration"""
+     
+     # Core Multi-Target Settings
+     MULTI_TARGET_ENABLED: bool = True  # Enable multi-target planning
+     MULTI_TARGET_MAX: int = 10  # Maximum targets per planning cycle
+     
+     # Turret Movement
+     TURRET_ROTATION_SPEED: float = 180.0  # degrees/second (matches EnvironmentConfig)
+     SHOT_DELAY: float = 0.1  # seconds between shots in sequence
+     
+     # Path Planning
+     PATH_PLANNING_TIMEOUT: float = 0.008  # 8ms max planning time
+     PATH_ALGORITHM: str = "adaptive"  # greedy, angle_sort, optimal, adaptive
+     
+     # Target Selection Weights
+     WEIGHT_DISTANCE: float = 0.4  # Weight for proximity
+     WEIGHT_THREAT: float = 0.3  # Weight for threat level (velocity toward center)
+     WEIGHT_CLUSTER: float = 0.2  # Weight for target clustering
+     WEIGHT_HIT_PROBABILITY: float = 0.1  # Weight for hit probability
+     
+     # Performance Thresholds
+     MIN_TARGETS_FOR_MULTI: int = 3  # Minimum targets to activate multi-target mode
+     MAX_ROTATION_PER_TARGET: float = 45.0  # Max degrees to rotate for next target
+     CLUSTER_RADIUS: float = 200.0  # Radius to consider targets clustered
+     
+     # Visualization
+     SHOW_PLANNED_PATH: bool = True  # Display planned engagement path
+     PATH_COLOR: Tuple[int, int, int] = (255, 255, 0)  # Yellow for path
+     PATH_ALPHA: float = 0.5  # Transparency for path visualization
+     SHOW_TARGET_NUMBERS: bool = True  # Number targets in sequence
+     
+     # Fallback Settings
+     FALLBACK_TO_SINGLE: bool = True  # Revert to single-target if planning fails
+     MAX_PLANNING_ATTEMPTS: int = 2  # Attempts before fallback
+ 
  
  @dataclass
```

---

Now I need to update the Config class to include the multi_target configuration:

---

```diff
          self.environment = EnvironmentConfig()
          self.targeting = TargetingConfig()
+         self.multi_target = MultiTargetConfig()
          self.brain = BrainConfig()
  
```

---

Now let's create the multi-target planner module:

---

